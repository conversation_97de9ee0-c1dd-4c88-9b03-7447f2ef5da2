/* eslint-disable no-unused-vars */
/* eslint-disable id-length */
const fs = require('fs');
const path = require('path');
const logger = require('../../../../common/utils/logger');
const AWS = require('aws-sdk');
const puppeteer = require('puppeteer');
require('dotenv').config();

const s3 = new AWS.S3({
    region: process.env.REGION,
    accessKeyId: process.env.ACCESSKEY_ID,
    secretAccessKey: process.env.SECRETACCESSKEY,
});

async function saveHtml(htmlbody) {
    if (typeof htmlbody !== 'string') {
        throw new Error('htmlbody is not a string!');
    }

    const filePath = path.join(__dirname, 'output.html');
    try {
        await fs.promises.writeFile(filePath, htmlbody, 'utf8');
        logger.info('File saved successfully!');
    } catch (err) {
        logger.error('Error saving file:', err);
        throw err;
    }
}

async function generatePdfFromHtml(htmlPath, pdfPath) {
    try {
        const browser = await puppeteer.launch();
        const page = await browser.newPage();

        const htmlContent = fs.readFileSync(htmlPath, 'utf-8');
        await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

        await page.pdf({ path: pdfPath, format: 'A4' });

        await browser.close();
        logger.info('PDF generated.');
    } catch (err) {
        logger.error('Error generating PDF:', err);
        throw err;
    }
}

// async function uploadPdfToS3(localFilePath, s3Bucket, s3Key) {
//     try {
//         if (!fs.existsSync(localFilePath)) {
//             throw new Error(`File not found: ${localFilePath}`);
//         }

//         const fileBuffer = fs.readFileSync(localFilePath);

//         const uploadParams = {
//             Bucket: s3Bucket,
//             Key: s3Key,
//             Body: fileBuffer,
//             ContentType: 'application/pdf',
//         };

//         await s3.upload(uploadParams).promise();
//         logger.info(`File uploaded to S3: s3://${s3Bucket}/${s3Key}`);
//     } catch (err) {
//         logger.error('Error uploading file to S3:', err);
//         throw err;
//     }
// }

module.exports = {
    saveHtml,
    generatePdfFromHtml,
    // uploadPdfToS3,
};

// const path = require('path');
// const { uploadPdfToS3 } = require('./uploadPdfToS3');

// (async () => {
//     const localFilePath = path.join(__dirname, 'reports/2025/aug/output.pdf');
//     const s3Bucket = 'your-s3-bucket-name';
//     const s3Key = 'documents/2025/aug/output.pdf';

//     await uploadPdfToS3(localFilePath, s3Bucket, s3Key);
// })();
