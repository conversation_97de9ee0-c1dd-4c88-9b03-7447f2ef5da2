/* eslint-disable id-length */
const Booking = require('../model/bookingModel');

const Counter = require('../counter/bookingCounter');

const logger = require('../../../common/utils/logger');

const AvailableTimeSlots = require('./bookingAvalabilityService');

const { BOOKING_ERROR_MESSAGES } = require('../../../common/utils/error');

const staffService = require('../../staff/service/staffService');

const serviceService = require('../../serviceInfo/service/serviceInfoService');

const { generateReferenceCode } = require('../counter/referenceCode');

const notificationService = require('../../../common/notification/email/notificationService');

const { connectMessage } = require('../../../common/utils/communicator');

// const createBooking = async (bookingData, customerId) => {
//     try {
//         const {
//             serviceId,
//             appointmentDate,
//             appointmentTimeFrom,
//             appointmentTimeTo,
//             staffId,
//             timeSlotId,
//         } = bookingData;
//         if (serviceId) {
//             const service =
//                 await serviceService.getServiceInformationById(serviceId);

//             if (!service) {
//                 throw new Error(BOOKING_ERROR_MESSAGES.SERVICE_NOT_FOUND);
//             }
//             const bid = await Counter.getNextSequence();
//             const bookingId = `BID_${bid}`;
//             const referenceCode = generateReferenceCode();
//             const newBookingData = {
//                 ...bookingData,
//                 bookingId,
//                 referenceCode,
//                 customerId,
//                 auditLogs: [
//                     {
//                         action: 'Created Booking',
//                         performedBy: customerId,
//                         timestamp: new Date(),
//                     },
//                 ],
//             };

//             newBookingData.bookingStatus = isBookingDataComplete(newBookingData)
//                 ? 'Pending'
//                 : 'Incomplete';

//             const booking = new Booking(newBookingData);
//             await AvailableTimeSlots.bookTimeSlot({
//                 serviceId,
//                 date: appointmentDate,
//                 from: appointmentTimeFrom,
//                 to: appointmentTimeTo,
//                 bookingId: bookingId,
//                 staffId: staffId,
//                 referenceCode,
//                 timeSlotId: timeSlotId,
//             });

//             const saved = await booking.save();

//             notificationService.sendBookingDetailsEmail(
//                 bookingData,
//                 customerId,
//                 referenceCode
//             );
//             logger.info(
//                 `Booking created: ${saved.bookingId} with status ${saved.bookingStatus}`
//             );
//             return saved;
//         }
//         // Ensure a value is always returned
//         return null;
//     } catch (error) {
//         logger.error('Error creating booking:', error);
//         throw new Error('Error creating booking: ' + error.message);
//     }
// };

const createBooking = async (bookingData, customerId) => {
    try {
        const {
            serviceId,
            appointmentDate,
            appointmentTimeFrom,
            appointmentTimeTo,
            staffId,
            timeSlotId,
            total,
            bookingStatus,
            providerId,
        } = bookingData;

        if (!serviceId) {
            throw new Error(
                BOOKING_ERROR_MESSAGES.SERVICE_ID_REQUIRED ||
                    'Service ID is required'
            );
        }

        const service =
            await serviceService.getServiceInformationById(serviceId);
        if (!service) {
            throw new Error(BOOKING_ERROR_MESSAGES.SERVICE_NOT_FOUND);
        }

        const bid = await Counter.getNextSequence();
        const bookingId = `BID_${bid}`;
        const referenceCode = generateReferenceCode();

        const newBookingData = {
            ...bookingData,
            bookingId,
            referenceCode,
            customerId,
            auditLogs: [
                {
                    action: 'Created Booking',
                    performedBy: customerId,
                    timestamp: new Date(),
                },
            ],
            bookingStatus: isBookingDataComplete(bookingData)
                ? 'Pending'
                : 'Incomplete',
        };

        const booking = new Booking(newBookingData);

        await AvailableTimeSlots.bookTimeSlot({
            serviceId,
            date: appointmentDate,
            from: appointmentTimeFrom,
            to: appointmentTimeTo,
            bookingId,
            staffId,
            referenceCode,
            timeSlotId,
        });

        const saved = await booking.save();

        notificationService.sendBookingDetailsEmail(
            bookingData,
            customerId,
            referenceCode
        );

        logger.info(
            `Booking created: ${saved.bookingId} with status ${saved.bookingStatus}`
        );

        connectMessage(
            serviceId,
            service.serviceTitle,
            appointmentDate,
            appointmentTimeFrom,
            appointmentTimeTo,
            total,
            bookingStatus,
            customerId,
            providerId,
            bookingId
        );
        return saved;
    } catch (error) {
        logger.error('Error creating booking:', error);
        throw new Error('Error creating booking: ' + error.message);
    }
};

const getBookings = async (query, sortBy, sortDirection, pageNum, limitNum) => {
    if (![1, -1].includes(sortDirection)) {
        throw new Error(BOOKING_ERROR_MESSAGES.INVALID_SORT_DIRECTION);
    }

    const skip = (pageNum - 1) * limitNum;

    try {
        logger.debug(`Fetching bookings with query: ${JSON.stringify(query)}`);
        logger.debug(
            `Pagination → page: ${pageNum}, limit: ${limitNum}, skip: ${skip}`
        );
        logger.debug(`Sorting by → ${sortBy} ${sortDirection}`);

        const rawBookings = await Booking.find(query)
            .sort({ updatedAt: -1, [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await Booking.countDocuments(query);

        logger.debug(
            `Raw bookings fetched: ${rawBookings.length}, Total matching: ${total}`
        );

        const bookings = await Promise.all(
            rawBookings.map((booking) =>
                getBookingById(booking.bookingId).catch((err) => {
                    logger.warn(
                        `Failed to build booking for ID ${booking.bookingId}: ${err.message}`
                    );
                    return null; // continue gracefully even if one booking fails
                })
            )
        );

        const validBookings = bookings.filter(Boolean); // remove nulls

        return { bookings: validBookings, total };
    } catch (error) {
        logger.error('Error fetching bookings:', error);
        throw new Error('Failed to retrieve bookings.');
    }
};

const buildBookingResponse = (booking, service) => {
    const additionalServices = (service.additionalServices || [])
        .map((svc) => ({
            id: svc.id,
            serviceItem: svc.serviceItem,
            price: svc.price,
            images: svc.images,
            selected: booking.additionalServiceIds.includes(svc.id),
        }))
        .filter((svc) => svc.selected); // only selected services

    return {
        bookingId: booking.bookingId,
        referenceCode: booking.referenceCode,
        customerId: booking.customerId,
        serviceId: booking.serviceId,
        serviceName: service.serviceTitle || null,
        providerId: booking.providerId,
        staffId: booking.staffId,
        serviceDetails: {
            title: service.serviceTitle,
            description: service.serviceOverview,
            price: service.price,
            offerPrice: service.offerPrice,
            priceAfterDiscount: service.priceAfterDiscount,
            duration: service.duration,
            includes: service.includes,
        },
        gallery: service.gallery || [],
        additionalServices,
        appointmentDate: booking.appointmentDate,
        appointmentTimeFrom: booking.appointmentTimeFrom,
        appointmentTimeTo: booking.appointmentTimeTo,
        bookingStatus: booking.bookingStatus,
        personalInfo: booking.personalInfo,
        cart: booking.cart,
        providerAcceptanceDate: booking.providerAcceptanceDate,
        completionDate: booking.completionDate,
        isPaid: booking.isPaid,
        paymentMethod: booking.paymentMethod,
        subtotal: booking.subtotal,
        tax: booking.tax,
        discount: booking.discount,
        total: booking.total,
        auditLogs: booking.auditLogs,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt,
    };
};

const getBookingById = async (bookingId) => {
    const booking = await Booking.findOne({ bookingId });
    if (!booking) return null;

    const service = await serviceService.getServiceInformationById(
        booking.serviceId
    );
    if (!service) return null;

    return buildBookingResponse(booking, service);
};

const getBookingByReferenceCode = async (referenceCode) => {
    const booking = await Booking.findOne({ referenceCode });
    if (!booking) return null;

    const service = await serviceService.getServiceInformationById(
        booking.serviceId
    );
    if (!service) return null;

    return buildBookingResponse(booking, service);
};

const updateBooking = async (bookingId, updateData) => {
    if (!bookingId) throw new Error('BookingId is required');

    const booking = await getBookingById(bookingId);
    if (!booking) throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);

    try {
        return await Booking.findOneAndUpdate(
            { bookingId },
            { $set: updateData },
            { new: true, runValidators: true }
        );
    } catch (error) {
        logger.error('Error updating booking:', error.message);
        throw new Error('Error updating booking: ' + error.message);
    }
};

// const updateBookingStatus = async (bookingId, newStatus, performedBy) => {
//     const allowedStatuses = Object.keys(validTransitions);
//     if (!allowedStatuses.includes(newStatus)) {
//         throw new Error(BOOKING_ERROR_MESSAGES.INVALID_BOOKING_STATUS);
//     }

//     const booking = await getBookingById(bookingId);
//     if (!booking) throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);

//     const currentStatus = booking.bookingStatus;

//     if (currentStatus === newStatus) {
//         throw new Error(BOOKING_ERROR_MESSAGES.STATUS_ALREADY_SET);
//     }

//     if (!validTransitions[currentStatus]?.includes(newStatus)) {
//         throw new Error(
//             BOOKING_ERROR_MESSAGES.INVALID_STATUS_TRANSITION(
//                 currentStatus,
//                 newStatus
//             )
//         );
//     }

//     if (newStatus === 'Pending' && !isBookingDataComplete(booking)) {
//         throw new Error(BOOKING_ERROR_MESSAGES.INCOMPLETE_BOOKING);
//     }

//     if (newStatus === 'Confirmed') {
//         if (!booking.providerAcceptanceDate) {
//             booking.providerAcceptanceDate = new Date();
//         }
//     }

//     if (newStatus === 'Cancelled') {
//         if (currentStatus === 'Completed') {
//             throw new Error(BOOKING_ERROR_MESSAGES.CANNOT_CANCEL_COMPLETED);
//         } else {
//             await AvailableTimeSlots.deleteBookingTimeSlots({
//                 serviceId: booking.serviceId,
//                 appointmentDate: booking.appointmentDate,
//                 bookingId: bookingId,
//             });
//         }
//     }

//     if (newStatus === 'Completed') {
//         if (currentStatus === 'Cancelled') {
//             throw new Error(BOOKING_ERROR_MESSAGES.CANNOT_COMPLETE_CANCELLED);
//         }

//         await AvailableTimeSlots.deleteBookingTimeSlots({
//             serviceId: booking.serviceId,
//             appointmentDate: booking.appointmentDate,
//             bookingId: bookingId,
//         });

//         staffService.incrementCompletedServices(booking.staffId);
//     }

//     try {
//         return await Booking.findOneAndUpdate(
//             { bookingId },
//             {
//                 $set: {
//                     bookingStatus: newStatus,
//                     ...(newStatus === 'Completed' && {
//                         completionDate: new Date(),
//                     }),
//                     ...(newStatus === 'Confirmed' && {
//                         providerAcceptanceDate: new Date(),
//                     }),
//                 },
//                 $push: {
//                     auditLogs: {
//                         action: `Status updated to ${newStatus}`,
//                         performedBy,
//                         timestamp: new Date(),
//                     },
//                 },
//             },
//             { new: true }
//         );
//     } catch (error) {
//         logger.error(`Error updating booking status: ${error.message}`);
//         throw new Error('Error updating booking status: ' + error.message);
//     }
// };
/////////////*************************************/////////////////// */
// const updateBookingStatus = async (bookingId, newStatus, note, performedBy) => {
//     const allowedStatuses = Object.keys(validTransitions);

//     // Step 1: If the new status is 'Confirmed', change it to 'InProgress'
//     let originalStatus = newStatus;
//     if (newStatus === 'Confirmed') {
//         newStatus = 'InProgress';
//     }

//     if (!allowedStatuses.includes(newStatus)) {
//         throw new Error(BOOKING_ERROR_MESSAGES.INVALID_BOOKING_STATUS);
//     }

//     const booking = await getBookingById(bookingId);
//     if (!booking) throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);

//     const currentStatus = booking.bookingStatus;

//     if (currentStatus === newStatus) {
//         throw new Error(BOOKING_ERROR_MESSAGES.STATUS_ALREADY_SET);
//     }

//     validateStatusTransition(currentStatus, newStatus);

//     if (newStatus === 'Pending' && !isBookingDataComplete(booking)) {
//         throw new Error(BOOKING_ERROR_MESSAGES.INCOMPLETE_BOOKING);
//     }

//     // Preserve 'Confirmed' behavior, like setting providerAcceptanceDate
//     if (originalStatus === 'Confirmed' && !booking.providerAcceptanceDate) {
//         booking.providerAcceptanceDate = new Date();
//     }

//     if (newStatus === 'Cancelled') {
//         await handleCancelledStatus(booking, currentStatus, bookingId);
//     }

//     if (newStatus === 'Completed') {
//         await handleCompletedStatus(booking, currentStatus, bookingId);
//     }

//     try {
//         return await Booking.findOneAndUpdate(
//             { bookingId },
//             {
//                 $set: {
//                     bookingStatus: newStatus,
//                     note: note || null,
//                     ...(newStatus === 'Completed' && {
//                         completionDate: new Date(),
//                     }),
//                     ...(originalStatus === 'Confirmed' && {
//                         providerAcceptanceDate: new Date(),
//                     }),
//                 },
//                 $push: {
//                     auditLogs: {
//                         action: `Status updated to ${newStatus} (original request: ${originalStatus})`,
//                         performedBy,
//                         timestamp: new Date(),
//                     },
//                 },
//             },
//             { new: true }
//         );
//     } catch (error) {
//         logger.error(`Error updating booking status: ${error.message}`);
//         throw new Error('Error updating booking status: ' + error.message);
//     }
// };

const validTransitions = {
    Incomplete: ['Pending'],
    Pending: ['Confirmed', 'Cancelled'],
    Confirmed: ['Completed', 'InProgress', 'Cancelled'],
    InProgress: ['Completed'],
    Completed: [],
    Cancelled: [],
};

const updateBookingStatus = async (bookingId, newStatus, note, performedBy) => {
    const allowedStatuses = Object.keys(validTransitions);
    if (!allowedStatuses.includes(newStatus)) {
        throw new Error(BOOKING_ERROR_MESSAGES.INVALID_BOOKING_STATUS);
    }

    const booking = await getBookingById(bookingId);
    const providerId = booking.providerId;
    const customerId = booking.customerId;
    if (!booking) {
        throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);
    }

    const currentStatus = booking.bookingStatus;

    if (currentStatus === newStatus) {
        throw new Error(BOOKING_ERROR_MESSAGES.STATUS_ALREADY_SET);
    }

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
        throw new Error(
            BOOKING_ERROR_MESSAGES.INVALID_STATUS_TRANSITION(
                currentStatus,
                newStatus
            )
        );
    }

    // Validate 'Pending' requires complete data
    if (newStatus === 'Pending' && !isBookingDataComplete(booking)) {
        throw new Error(BOOKING_ERROR_MESSAGES.INCOMPLETE_BOOKING);
    }

    // Set providerAcceptanceDate if status becomes Confirmed
    if (newStatus === 'Confirmed' && !booking.providerAcceptanceDate) {
        booking.providerAcceptanceDate = new Date();
    }

    // Handle Cancelled status
    if (newStatus === 'Cancelled') {
        if (currentStatus === 'Completed') {
            throw new Error(BOOKING_ERROR_MESSAGES.CANNOT_CANCEL_COMPLETED);
        }

        // Prevent cancelling a Confirmed booking within 24 hours of the appointment
        if (currentStatus === 'Confirmed') {
            const now = new Date();
            const appointmentDate = new Date(booking.appointmentDate);
            const hoursDifference = (appointmentDate - now) / (1000 * 60 * 60); // convert ms to hours

            if (hoursDifference < 24) {
                throw new Error(
                    BOOKING_ERROR_MESSAGES.CANNOT_CANCEL_WITHIN_24_HOURS
                );
            }
        }

        await AvailableTimeSlots.deleteBookingTimeSlots({
            serviceId: booking.serviceId,
            appointmentDate: booking.appointmentDate,
            bookingId,
        });
    }

    // Handle Completed status
    if (newStatus === 'Completed') {
        if (currentStatus === 'Cancelled') {
            throw new Error(BOOKING_ERROR_MESSAGES.CANNOT_COMPLETE_CANCELLED);
        }

        await AvailableTimeSlots.deleteBookingTimeSlots({
            serviceId: booking.serviceId,
            appointmentDate: booking.appointmentDate,
            bookingId,
        });

        staffService.incrementCompletedServices(booking.staffId);
    }

    try {
        // Step 1: Save the new status
        const updatedBooking = await Booking.findOneAndUpdate(
            { bookingId },
            {
                $set: {
                    bookingStatus: newStatus,
                    note: note || null,
                    ...(newStatus === 'Completed' && {
                        completionDate: new Date(),
                    }),
                    ...(newStatus === 'Confirmed' && {
                        providerAcceptanceDate: new Date(),
                    }),
                },
                $push: {
                    auditLogs: {
                        action: `Status updated to ${newStatus}`,
                        performedBy,
                        timestamp: new Date(),
                    },
                },
            },
            { new: true }
        );
        notificationService.bookingStatusUpdateEmail(
            customerId,
            providerId,
            booking.referenceCode,
            newStatus
        );

        connectMessage({
            serviceId: booking.serviceId,
            serviceTitle: service.serviceTitle,
            appointmentDate: booking.appointmentDate,
            appointmentTimeFrom: booking.appointmentTimeFrom,
            appointmentTimeTo: booking.appointmentTimeTo,
            total: booking.total,
            bookingStatus: newStatus,
            customerId,
            providerId,
            bookingId,
        });

        return updatedBooking;
    } catch (error) {
        logger.error(`Error updating booking status: ${error.message}`);
        throw new Error('Error updating booking status: ' + error.message);
    }
};

const rescheduleBooking = async (
    bookingId,
    staffId,
    newDate,
    newFrom,
    newTo,
    note,
    performedBy
) => {
    const booking = await getBookingById(bookingId);

    if (!booking) throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);

    const oldDate = booking.appointmentDate;

    try {
        // Delete the old time slot
        await AvailableTimeSlots.deleteBookingTimeSlots({
            serviceId: booking.serviceId,
            appointmentDate: oldDate,
            bookingId,
        });

        // Book the new time slot
        await AvailableTimeSlots.bookTimeSlot({
            serviceId: booking.serviceId,
            date: newDate,
            from: newFrom,
            to: newTo,
            bookingId,
            staffId,
        });

        // Update the booking document
        const updatedBooking = await Booking.findOneAndUpdate(
            { bookingId },
            {
                $set: {
                    appointmentDate: newDate,
                    appointmentTimeFrom: newFrom,
                    appointmentTimeTo: newTo,
                    staffId,
                    note: note || null,
                    bookingStatus: 'Pending',
                },
                $push: {
                    auditLogs: {
                        action: `Rescheduled Booking`,
                        performedBy,
                        timestamp: new Date(),
                    },
                },
            },
            { new: true }
        );

        logger.info(
            `Booking ${bookingId} rescheduled to ${newDate} ${newFrom}-${newTo}`
        );

        notificationService.bookingStatusUpdateEmail(
            booking.customerId,
            booking.providerId,
            booking.referenceCode,
            'rescheduled'
        );

        connectMessage({
            serviceId: booking.serviceId,
            serviceTitle: service.serviceTitle,
            appointmentDate: booking.appointmentDate,
            appointmentTimeFrom: booking.appointmentTimeFrom,
            appointmentTimeTo: booking.appointmentTimeTo,
            total: booking.total,
            bookingStatus: 'rescheduled',
            customerId: booking.customerId,
            providerId: booking.providerId,
            bookingId,
        });

        return updatedBooking;
    } catch (error) {
        logger.error(
            `Error rescheduling booking ${bookingId}: ${error.message}`
        );
        throw new Error('Error rescheduling booking: ' + error.message);
    }
};

const deleteBooking = async (bookingId) => {
    if (!bookingId) throw new Error('BookingId is required');

    try {
        const booking = await getBookingById(bookingId);
        const providerId = booking.providerId;
        const customerId = booking.customerId;
        if (!booking) throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);

        await AvailableTimeSlots.deleteBookingTimeSlots({
            serviceId: booking.serviceId,
            appointmentDate: booking.appointmentDate,
            bookingId: bookingId,
        });

        const deleted = await Booking.findOneAndDelete({ bookingId });

        if (!deleted) {
            throw new Error(BOOKING_ERROR_MESSAGES.BOOKING_NOT_FOUND);
        }
        notificationService.sendDeleteBookingEmail(
            customerId,
            providerId,
            booking.referenceCode
        );
        return deleted;
    } catch (error) {
        logger.error(`Error deleting booking ${bookingId}: ${error.message}`);
        throw new Error('Error deleting booking: ' + error.message);
    }
};

// const isBookingDataComplete = (booking) => {
//     const hasCartItems = Array.isArray(booking.cart) && booking.cart.length > 0;
//     const cartValid =
//         hasCartItems &&
//         booking.cart.every(
//             (item) =>
//                 item.serviceId && item.name && typeof item.price === 'number'
//         );

//     const info = booking.personalInfo || {};

//     return (
//         booking.appointmentDate &&
//         booking.appointmentTimeSlot &&
//         info.firstName &&
//         info.lastName &&
//         info.email &&
//         info.phone &&
//         cartValid
//     );
// };

const isBookingDataComplete = (booking) => {
    // const hasCartItems = Array.isArray(booking.cart) && booking.cart.length > 0;
    // const cartValid =
    //     hasCartItems &&
    //     booking.cart.every(
    //         (item) =>
    //             item.serviceId && item.name && typeof item.price === 'number'
    //     );

    const info = booking.personalInfo || {};

    return (
        booking.appointmentDate &&
        booking.appointmentTimeFrom &&
        booking.appointmentTimeTo &&
        info.firstName &&
        info.lastName &&
        info.email &&
        info.phone
    );
};

// function validateStatusTransition(currentStatus, newStatus) {
//     if (!validTransitions[currentStatus]?.includes(newStatus)) {
//         throw new Error(
//             BOOKING_ERROR_MESSAGES.INVALID_STATUS_TRANSITION(
//                 currentStatus,
//                 newStatus
//             )
//         );
//     }
// }

// async function handleCancelledStatus(booking, currentStatus, bookingId) {
//     if (currentStatus === 'Completed') {
//         throw new Error(BOOKING_ERROR_MESSAGES.CANNOT_CANCEL_COMPLETED);
//     }
//     await AvailableTimeSlots.deleteBookingTimeSlots({
//         serviceId: booking.serviceId,
//         appointmentDate: booking.appointmentDate,
//         bookingId: bookingId,
//     });
// }

// async function handleCompletedStatus(booking, currentStatus, bookingId) {
//     if (currentStatus === 'Cancelled') {
//         throw new Error(BOOKING_ERROR_MESSAGES.CANNOT_COMPLETE_CANCELLED);
//     }
//     await AvailableTimeSlots.deleteBookingTimeSlots({
//         serviceId: booking.serviceId,
//         appointmentDate: booking.appointmentDate,
//         bookingId: bookingId,
//     });
//     staffService.incrementCompletedServices(booking.staffId);
// }

module.exports = {
    createBooking,
    getBookings,
    getBookingById,
    updateBooking,
    updateBookingStatus,
    deleteBooking,
    rescheduleBooking,
    getBookingByReferenceCode,
};
